# FiveM Tag System

Ein FiveM Script, das es Spielern ermöglicht, grafische Tags über ihren Köpfen anzuzeigen.

## Features

- **6 verschiedene Tag-Typen**: VIP, Ad<PERSON>, Moderator, Player, Streamer, Developer
- **Animierte Tags**: Schwebende Animation für bessere Sichtbarkeit
- **Distanz-basierte Skalierung**: <PERSON><PERSON> werden kleiner, je weiter entfernt sie sind
- **Hintergrund-Support**: Tags mit halbtransparentem Hintergrund für bessere Lesbarkeit
- **Berechtigungssystem**: Optional konfigurierbare Berechtigungen für bestimmte Tags
- **Admin-Befehle**: Admins können Tags für andere Spieler setzen

## Installation

1. Kopiere den `TagSystem` Ordner in deinen `resources` Ordner
2. Füge `ensure TagSystem` zu deiner `server.cfg` hinzu
3. Starte den Server neu

## Befehle

### Für Spieler:
- `/tag` - <PERSON>eigt das Tag-Menü an
- `/tag [nummer]` - Setzt ein bestimmtes Tag (1-6)
- `/tag 0` - Entfernt das aktuelle Tag
- `/listtags` - Zeigt alle verfügbaren Tags an

### Für Admins:
- `/settag <playerId> <tagId>` - Setzt ein Tag für einen anderen Spieler
- `/settag <playerId> 0` - Entfernt das Tag eines anderen Spielers

## Verfügbare Tags

1. **VIP** - ★ VIP ★ (Gold)
2. **Admin** - ⚡ ADMIN ⚡ (Rot)
3. **Moderator** - 🛡️ MOD 🛡️ (Grün)
4. **Player** - 👤 PLAYER (Weiß)
5. **Streamer** - 📺 LIVE (Magenta)
6. **Developer** - 💻 DEV (Cyan)

## Konfiguration

Bearbeite die `config.lua` Datei um folgende Einstellungen anzupassen:

### Grundeinstellungen:
- `Config.TagCommand` - Befehl zum Öffnen des Tag-Menüs
- `Config.TagHeight` - Höhe des Tags über dem Kopf
- `Config.TagDistance` - Maximale Sichtweite für Tags
- `Config.TagScale` - Größe des Tag-Texts
- `Config.DefaultTag` - Standard-Tag für neue Spieler

### Animation:
- `Config.EnableAnimation` - Aktiviert/Deaktiviert die schwebende Animation
- `Config.AnimationSpeed` - Geschwindigkeit der Animation
- `Config.AnimationHeight` - Höhe der Auf-und-Ab-Bewegung

### Berechtigungen:
- `Config.RequirePermission` - Aktiviert das Berechtigungssystem
- `Config.TagPermissions` - ACE-Berechtigungen für jeden Tag-Typ

### Neue Tags hinzufügen:

```lua
{
    id = 7,
    name = "Neuer Tag",
    text = "🎯 NEU",
    color = {255, 128, 0, 255}, -- Orange
    background = true,
    backgroundColor = {0, 0, 0, 150}
}
```

## Berechtigungen (ACE)

Wenn `Config.RequirePermission = true` ist, werden folgende Berechtigungen benötigt:

- `tag.vip` - Für VIP Tag
- `tag.admin` - Für Admin Tag
- `tag.moderator` - Für Moderator Tag
- `tag.player` - Für Player Tag
- `tag.streamer` - Für Streamer Tag
- `tag.developer` - Für Developer Tag

Beispiel für `server.cfg`:
```
add_ace group.admin tag.admin allow
add_ace group.moderator tag.moderator allow
add_ace identifier.steam:110000112345678 tag.vip allow
```

## Technische Details

- **Client-Server Synchronisation**: Tags werden zwischen allen Clients synchronisiert
- **Performance-optimiert**: Tags werden nur in der konfigurierten Reichweite gerendert
- **3D-Rendering**: Verwendet World3dToScreen2d für präzise Positionierung
- **Automatische Bereinigung**: Tags werden automatisch entfernt wenn Spieler den Server verlassen

## Troubleshooting

### Tags werden nicht angezeigt:
1. Überprüfe ob das Script korrekt gestartet wurde (`ensure TagSystem` in server.cfg)
2. Überprüfe die F8-Konsole auf Fehlermeldungen
3. Stelle sicher, dass die Distanz-Einstellung hoch genug ist

### Berechtigungsfehler:
1. Überprüfe ob `Config.RequirePermission` korrekt konfiguriert ist
2. Stelle sicher, dass die ACE-Berechtigungen korrekt gesetzt sind
3. Verwende `/listtags` um verfügbare Tags zu sehen

## Support

Bei Problemen oder Fragen erstelle ein Issue im Repository oder kontaktiere den Entwickler.

## Version

**Version 1.0.0**
- Initiale Veröffentlichung
- 6 Standard-Tags
- Animationssystem
- Berechtigungssystem
- Admin-Befehle
