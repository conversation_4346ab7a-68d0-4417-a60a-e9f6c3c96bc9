local playerTags = {} -- Store all player tags
local myTag = nil -- Current player's tag
local animationOffset = 0.0 -- For floating animation
local loadedTextures = {} -- Cache for loaded textures

-- Enhanced image loading system
local imageCache = {}
local uiLoaded = false

-- Check if YTD texture exists
function CheckYTDExists(imageName)
    if imageCache[imageName] ~= nil then
        return imageCache[imageName]
    end

    local textureName = imageName:gsub('%.%w+$', '') -- Remove extension
    local txdName = 'tagsystem_' .. textureName

    -- Check if YTD texture dictionary is loaded
    local exists = HasStreamedTextureDictLoaded(txdName)

    if not exists then
        -- Try to request it
        RequestStreamedTextureDict(txdName, false)

        -- Wait a bit and check again
        local timeout = GetGameTimer() + 1000 -- 1 second timeout
        while not HasStreamedTextureDictLoaded(txdName) and GetGameTimer() < timeout do
            Citizen.Wait(10)
        end

        exists = HasStreamedTextureDictLoaded(txdName)
    end

    imageCache[imageName] = exists
    print(string.format("YTD %s (%s) exists: %s", txdName, imageName, tostring(exists)))
    return exists
end

-- Load texture function (YTD optimized)
function LoadTexture(imageName)
    if not loadedTextures[imageName] then
        local textureName = imageName:gsub('%.%w+$', '') -- Remove file extension
        local txdName = 'tagsystem_' .. textureName

        -- Check if YTD texture exists
        local ytdExists = CheckYTDExists(imageName)

        if ytdExists then
            loadedTextures[imageName] = {
                txd = txdName,
                texture = textureName,
                loaded = true,
                isYTD = true
            }
            print("Loaded YTD texture: " .. txdName .. " -> " .. textureName)
        else
            -- Fallback to built-in graphics
            loadedTextures[imageName] = {
                txd = 'commonmenu',
                texture = 'gradient_bgd',
                loaded = false,
                custom = true -- Mark as custom graphics
            }
            print("Using custom graphics for: " .. imageName)
        end
    end
    return loadedTextures[imageName]
end

-- Initialize
Citizen.CreateThread(function()
    -- Pre-load all tag images
    for _, tagConfig in ipairs(Config.TagTypes) do
        if tagConfig.type == "image" and tagConfig.image then
            LoadTexture(tagConfig.image)
        end
    end

    -- Request tag data from server
    TriggerServerEvent('tagsystem:requestPlayerTags')

    while true do
        Citizen.Wait(0)

        -- Update animation
        if Config.EnableAnimation then
            animationOffset = math.sin(GetGameTimer() / 1000.0 * Config.AnimationSpeed) * Config.AnimationHeight
        end

        -- Draw all player tags
        DrawAllPlayerTags()
    end
end)

-- Debug variables
local debugActive = false
local debugEndTime = 0

-- Debug command to test tag visibility
RegisterCommand('tagdebug', function()
    debugActive = true
    debugEndTime = GetGameTimer() + 10000 -- 10 seconds
    Notify("Debug: Test-Tag für 10 Sekunden aktiviert")
end, false)

-- Debug command to stop test
RegisterCommand('tagdebugstop', function()
    debugActive = false
    Notify("Debug: Test-Tag deaktiviert")
end, false)

-- Simple test command
RegisterCommand('tagtest', function()
    if myTag then
        local tagConfig = Config.TagTypes[myTag]
        Notify("Aktueller Tag: " .. tagConfig.name .. " (Typ: " .. tagConfig.type .. ")")
    else
        Notify("Kein Tag gesetzt")
    end

    -- Show debug info
    local playerCount = 0
    for _ in pairs(playerTags) do
        playerCount = playerCount + 1
    end
    Notify("Spieler mit Tags: " .. playerCount)

    -- Show loaded textures
    local textureCount = 0
    for imageName, _ in pairs(loadedTextures) do
        textureCount = textureCount + 1
        print("Loaded texture: " .. imageName)
    end
    Notify("Geladene Texturen: " .. textureCount)
end, false)

-- Image test command
RegisterCommand('tagimagetest', function(source, args)
    local imageId = tonumber(args[1]) or 1
    local tagConfig = Config.TagTypes[imageId]

    if tagConfig and tagConfig.type == "image" then
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)

        Notify("Teste Bild: " .. tagConfig.image)

        -- Check if YTD exists
        local ytdExists = CheckYTDExists(tagConfig.image)
        print("YTD exists: " .. tostring(ytdExists))

        if ytdExists then
            Notify("✅ YTD gefunden - lade Texture")
            local ytdTexture = LoadYTDTexture(tagConfig.image)
            if ytdTexture then
                Notify("✅ YTD Texture geladen!")
            else
                Notify("❌ YTD Texture fehlgeschlagen - verwende Fallback")
            end
        else
            Notify("❌ YTD nicht gefunden - verwende Custom Graphics")
        end

        -- Draw test image for 10 seconds
        local endTime = GetGameTimer() + 10000
        Citizen.CreateThread(function()
            while GetGameTimer() < endTime do
                Citizen.Wait(0)
                DrawSprite3D(coords.x, coords.y, coords.z + 2.0, tagConfig.image, tagConfig.size, 1.0)
            end
            Notify("Bild-Test beendet")
        end)
    else
        Notify("Tag " .. imageId .. " ist kein Bild-Tag")
    end
end, false)

-- Register tag command
RegisterCommand(Config.TagCommand, function(source, args)
    if args[1] then
        local tagId = tonumber(args[1])
        if tagId and tagId >= 1 and tagId <= #Config.TagTypes then
            TriggerServerEvent('tagsystem:setTag', tagId)
        else
            ShowTagMenu()
        end
    else
        ShowTagMenu()
    end
end, false)

-- Show tag selection menu
function ShowTagMenu()
    local menuText = "~b~Tag System~w~\n\nVerfügbare Tags:\n"
    
    for i, tag in ipairs(Config.TagTypes) do
        menuText = menuText .. string.format("~y~%d~w~. %s\n", i, tag.name)
    end
    
    menuText = menuText .. "\n~r~0~w~. Tag entfernen\n"
    menuText = menuText .. "\nVerwendung: /" .. Config.TagCommand .. " [nummer]"
    
    -- Show notification with menu
    SetNotificationTextEntry("STRING")
    AddTextComponentString(menuText)
    DrawNotification(false, false)
    
    -- Also show in chat
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"Tag System", menuText}
    })
end

-- Draw all player tags
function DrawAllPlayerTags()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local myServerId = GetPlayerServerId(PlayerId())

    -- Draw debug tag if active
    if debugActive then
        if GetGameTimer() > debugEndTime then
            debugActive = false
            Notify("Debug: Test-Tag beendet")
        else
            DrawText3DSimple(playerCoords.x, playerCoords.y, playerCoords.z + 2.0, "★ DEBUG TEST ★", 1.0, {255, 255, 0, 255})
        end
    end

    -- Draw my own tag if I have one
    if myTag then
        local myTagData = { tagId = myTag }
        DrawPlayerTag(playerPed, myTagData, 0)
    end

    -- Draw other players' tags
    for i = 0, 255 do
        if i ~= PlayerId() then
            local targetPed = GetPlayerPed(i)
            local targetServerId = GetPlayerServerId(i)

            if targetPed and targetPed ~= 0 and DoesEntityExist(targetPed) and targetServerId ~= -1 then
                local tagData = playerTags[targetServerId]

                if tagData then
                    local targetCoords = GetEntityCoords(targetPed)
                    local distance = #(playerCoords - targetCoords)

                    if distance <= Config.TagDistance then
                        DrawPlayerTag(targetPed, tagData, distance)
                    end
                end
            end
        end
    end
end

-- Draw individual player tag
function DrawPlayerTag(ped, tagData, distance)
    local tagConfig = Config.TagTypes[tagData.tagId]
    if not tagConfig then return end

    local pedCoords = GetEntityCoords(ped)
    local tagHeight = Config.TagHeight + animationOffset

    -- Calculate tag position above head
    local tagCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z + tagHeight)

    -- Calculate scale based on distance
    local scale = Config.TagScale
    if distance > 0 then
        scale = Config.TagScale * (1.0 - (distance / Config.TagDistance) * 0.3)
        scale = math.max(scale, 0.3)
    end

    -- Draw image or text based on tag type
    if tagConfig.type == "image" and tagConfig.image then
        DrawSprite3D(tagCoords.x, tagCoords.y, tagCoords.z, tagConfig.image, tagConfig.size, scale)
    else
        -- Fallback to text
        DrawText3DSimple(tagCoords.x, tagCoords.y, tagCoords.z, tagConfig.text, scale, tagConfig.color)
    end
end

-- YTD texture loading for custom images
local ytdTextures = {}

-- Load YTD texture
function LoadYTDTexture(imageName)
    if ytdTextures[imageName] then
        return ytdTextures[imageName]
    end

    local textureName = imageName:gsub('%.%w+$', '') -- Remove extension
    local txdName = 'tagsystem_' .. textureName

    -- Check if YTD is loaded
    if HasStreamedTextureDictLoaded(txdName) then
        ytdTextures[imageName] = {
            txd = txdName,
            texture = textureName,
            loaded = true
        }
        print("YTD texture ready: " .. txdName .. " -> " .. textureName)
        return ytdTextures[imageName]
    else
        print("YTD texture not loaded: " .. txdName)
        return nil
    end
end

-- Draw 3D sprite/image with YTD or custom graphics
function DrawSprite3D(x, y, z, imageName, size, scale)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)

    if onScreen then
        local width = size[1] * scale
        local height = size[2] * scale

        -- Try to load YTD texture first
        local ytdTexture = LoadYTDTexture(imageName)

        if ytdTexture and ytdTexture.loaded then
            -- Draw YTD texture with debug info
            print(string.format("Drawing YTD: %s -> %s at %.2f, %.2f (%.3f x %.3f)",
                ytdTexture.txd, ytdTexture.texture, _x, _y, width, height))

            -- Try different color values to ensure visibility
            DrawSprite(ytdTexture.txd, ytdTexture.texture, _x, _y, width, height, 0.0, 255, 255, 255, 255)

            -- Try with different blend modes
            -- DrawSprite(ytdTexture.txd, ytdTexture.texture, _x, _y, width, height, 0.0, 255, 0, 0, 255) -- Red tint
            -- DrawSprite(ytdTexture.txd, ytdTexture.texture, _x, _y, width, height, 0.0, 0, 255, 0, 255) -- Green tint

            -- Add a colored border for debugging
            local borderSize = 0.002
            DrawRect(_x, _y - height/2 + borderSize/2, width, borderSize, 0, 255, 0, 255) -- Green top
            DrawRect(_x, _y + height/2 - borderSize/2, width, borderSize, 0, 255, 0, 255) -- Green bottom
            DrawRect(_x - width/2 + borderSize/2, _y, borderSize, height, 0, 255, 0, 255) -- Green left
            DrawRect(_x + width/2 - borderSize/2, _y, borderSize, height, 0, 255, 0, 255) -- Green right

            return
        end

        -- Fallback to custom graphics based on image name
        if imageName == "admin_tag.png" then
            DrawAdminTag(_x, _y, width, height)
        elseif imageName == "vip_tag.png" then
            DrawVIPTag(_x, _y, width, height)
        elseif imageName == "streamer_tag.png" then
            DrawStreamerTag(_x, _y, width, height)
        elseif imageName == "mod_tag.png" then
            DrawModTag(_x, _y, width, height)
        elseif imageName == "dev_tag.png" then
            DrawDevTag(_x, _y, width, height)
        else
            -- Default fallback: colored rectangle
            DrawRect(_x, _y, width, height, 100, 100, 100, 200)
        end
    end
end

-- Simple and reliable 3D text function
function DrawText3DSimple(x, y, z, text, scale, color)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)

    if onScreen then
        -- First draw background rectangle
        local bgWidth = string.len(text) * scale * 0.01
        local bgHeight = scale * 0.02
        DrawRect(_x, _y, bgWidth, bgHeight, 0, 0, 0, 150)

        -- Then draw text
        SetTextScale(scale, scale)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(color[1], color[2], color[3], color[4] or 255)
        SetTextOutline()
        SetTextDropShadow()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Custom tag drawing functions

-- Admin Tag (Brain design)
function DrawAdminTag(x, y, width, height)
    -- Dark background
    DrawRect(x, y, width, height, 20, 20, 20, 200)

    -- Cyan/blue border (like the brain image)
    local borderSize = 0.002
    DrawRect(x, y - height/2 + borderSize/2, width, borderSize, 0, 255, 255, 255) -- Top
    DrawRect(x, y + height/2 - borderSize/2, width, borderSize, 0, 255, 255, 255) -- Bottom
    DrawRect(x - width/2 + borderSize/2, y, borderSize, height, 0, 255, 255, 255) -- Left
    DrawRect(x + width/2 - borderSize/2, y, borderSize, height, 0, 255, 255, 255) -- Right

    -- Brain hemispheres (pink/red like the image)
    local centerX, centerY = x, y
    local brainSize = width * 0.35

    -- Left brain hemisphere
    local leftX = centerX - brainSize * 0.3
    local leftY = centerY
    DrawRect(leftX, leftY, brainSize * 0.8, brainSize * 0.9, 255, 100, 120, 255) -- Pink base
    DrawRect(leftX, leftY, brainSize * 0.6, brainSize * 0.7, 255, 150, 170, 255) -- Lighter pink

    -- Right brain hemisphere
    local rightX = centerX + brainSize * 0.3
    local rightY = centerY
    DrawRect(rightX, rightY, brainSize * 0.8, brainSize * 0.9, 255, 100, 120, 255) -- Pink base
    DrawRect(rightX, rightY, brainSize * 0.6, brainSize * 0.7, 255, 150, 170, 255) -- Lighter pink

    -- Brain details/wrinkles (darker lines)
    local detailSize = brainSize * 0.1
    DrawRect(leftX - brainSize * 0.1, leftY - brainSize * 0.2, detailSize, brainSize * 0.4, 200, 50, 70, 255)
    DrawRect(leftX + brainSize * 0.1, leftY + brainSize * 0.1, detailSize, brainSize * 0.3, 200, 50, 70, 255)
    DrawRect(rightX - brainSize * 0.1, rightY - brainSize * 0.1, detailSize, brainSize * 0.3, 200, 50, 70, 255)
    DrawRect(rightX + brainSize * 0.1, rightY + brainSize * 0.2, detailSize, brainSize * 0.4, 200, 50, 70, 255)

    -- Central connection (corpus callosum)
    DrawRect(centerX, centerY, brainSize * 0.2, brainSize * 0.1, 255, 200, 220, 255)

    -- White highlights (like the image)
    DrawRect(leftX - brainSize * 0.2, leftY - brainSize * 0.3, brainSize * 0.3, brainSize * 0.2, 255, 255, 255, 200)
    DrawRect(rightX + brainSize * 0.1, rightY - brainSize * 0.2, brainSize * 0.2, brainSize * 0.15, 255, 255, 255, 200)
end

-- VIP Tag
function DrawVIPTag(x, y, width, height)
    -- Gold background
    DrawRect(x, y, width, height, 255, 215, 0, 200)

    -- Black border
    local borderSize = 0.002
    DrawRect(x, y - height/2 + borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x, y + height/2 - borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x - width/2 + borderSize/2, y, borderSize, height, 0, 0, 0, 255)
    DrawRect(x + width/2 - borderSize/2, y, borderSize, height, 0, 0, 0, 255)

    -- Star shape (simplified as diamond)
    local starSize = width * 0.25
    DrawRect(x, y, starSize, starSize, 255, 255, 255, 255)
end

-- Moderator Tag
function DrawModTag(x, y, width, height)
    -- Green background
    DrawRect(x, y, width, height, 0, 255, 0, 200)

    -- Black border
    local borderSize = 0.002
    DrawRect(x, y - height/2 + borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x, y + height/2 - borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x - width/2 + borderSize/2, y, borderSize, height, 0, 0, 0, 255)
    DrawRect(x + width/2 - borderSize/2, y, borderSize, height, 0, 0, 0, 255)

    -- Shield shape
    local shieldSize = width * 0.3
    DrawRect(x, y, shieldSize, shieldSize * 1.2, 255, 255, 255, 255)
end

-- Streamer Tag
function DrawStreamerTag(x, y, width, height)
    -- Purple background
    DrawRect(x, y, width, height, 255, 0, 255, 200)

    -- White border
    local borderSize = 0.002
    DrawRect(x, y - height/2 + borderSize/2, width, borderSize, 255, 255, 255, 255)
    DrawRect(x, y + height/2 - borderSize/2, width, borderSize, 255, 255, 255, 255)
    DrawRect(x - width/2 + borderSize/2, y, borderSize, height, 255, 255, 255, 255)
    DrawRect(x + width/2 - borderSize/2, y, borderSize, height, 255, 255, 255, 255)

    -- "LIVE" indicator
    local liveSize = width * 0.6
    DrawRect(x, y, liveSize, height * 0.4, 255, 255, 255, 255)
    DrawRect(x, y, liveSize * 0.8, height * 0.2, 255, 0, 0, 255)
end

-- Developer Tag
function DrawDevTag(x, y, width, height)
    -- Cyan background
    DrawRect(x, y, width, height, 0, 255, 255, 200)

    -- Black border
    local borderSize = 0.002
    DrawRect(x, y - height/2 + borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x, y + height/2 - borderSize/2, width, borderSize, 0, 0, 0, 255)
    DrawRect(x - width/2 + borderSize/2, y, borderSize, height, 0, 0, 0, 255)
    DrawRect(x + width/2 - borderSize/2, y, borderSize, height, 0, 0, 0, 255)

    -- Code brackets
    local bracketSize = width * 0.15
    DrawRect(x - width * 0.2, y, bracketSize * 0.3, height * 0.6, 0, 0, 0, 255) -- Left bracket
    DrawRect(x + width * 0.2, y, bracketSize * 0.3, height * 0.6, 0, 0, 0, 255) -- Right bracket
end

-- Event handlers
RegisterNetEvent('tagsystem:updatePlayerTag')
AddEventHandler('tagsystem:updatePlayerTag', function(playerId, tagData)
    if tagData then
        playerTags[playerId] = tagData
    else
        playerTags[playerId] = nil
    end
end)

RegisterNetEvent('tagsystem:updateAllTags')
AddEventHandler('tagsystem:updateAllTags', function(allTags)
    playerTags = allTags
end)

RegisterNetEvent('tagsystem:tagSet')
AddEventHandler('tagsystem:tagSet', function(tagId)
    if tagId == 0 then
        myTag = nil
        Notify("Tag entfernt")
    else
        myTag = tagId
        local tagConfig = Config.TagTypes[tagId]
        if tagConfig then
            Notify("Tag gesetzt: " .. tagConfig.name)
        end
    end
end)

RegisterNetEvent('tagsystem:tagError')
AddEventHandler('tagsystem:tagError', function(message)
    Notify("Fehler: " .. message)
end)

-- Utility function for notifications
function Notify(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)
end

-- Clean up when player leaves
AddEventHandler('playerDropped', function(playerId)
    playerTags[playerId] = nil
end)
