local playerTags = {} -- Store all player tags
local myTag = nil -- Current player's tag
local animationOffset = 0.0 -- For floating animation

-- Initialize
Citizen.CreateThread(function()
    -- Request tag data from server
    TriggerServerEvent('tagsystem:requestPlayerTags')
    
    while true do
        Citizen.Wait(0)
        
        -- Update animation
        if Config.EnableAnimation then
            animationOffset = math.sin(GetGameTimer() / 1000.0 * Config.AnimationSpeed) * Config.AnimationHeight
        end
        
        -- Draw all player tags
        DrawAllPlayerTags()
    end
end)

-- Register tag command
RegisterCommand(Config.TagCommand, function(source, args)
    if args[1] then
        local tagId = tonumber(args[1])
        if tagId and tagId >= 1 and tagId <= #Config.TagTypes then
            TriggerServerEvent('tagsystem:setTag', tagId)
        else
            ShowTagMenu()
        end
    else
        ShowTagMenu()
    end
end, false)

-- Show tag selection menu
function ShowTagMenu()
    local menuText = "~b~Tag System~w~\n\nVerfügbare Tags:\n"
    
    for i, tag in ipairs(Config.TagTypes) do
        menuText = menuText .. string.format("~y~%d~w~. %s\n", i, tag.name)
    end
    
    menuText = menuText .. "\n~r~0~w~. Tag entfernen\n"
    menuText = menuText .. "\nVerwendung: /" .. Config.TagCommand .. " [nummer]"
    
    -- Show notification with menu
    SetNotificationTextEntry("STRING")
    AddTextComponentString(menuText)
    DrawNotification(false, false)
    
    -- Also show in chat
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"Tag System", menuText}
    })
end

-- Draw all player tags
function DrawAllPlayerTags()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    for playerId, tagData in pairs(playerTags) do
        local targetPed = GetPlayerPed(playerId)
        
        if targetPed and targetPed ~= 0 and DoesEntityExist(targetPed) then
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(playerCoords - targetCoords)
            
            if distance <= Config.TagDistance then
                DrawPlayerTag(targetPed, tagData, distance)
            end
        end
    end
end

-- Draw individual player tag
function DrawPlayerTag(ped, tagData, distance)
    local tagConfig = Config.TagTypes[tagData.tagId]
    if not tagConfig then return end
    
    local pedCoords = GetEntityCoords(ped)
    local tagHeight = Config.TagHeight + animationOffset
    
    -- Calculate tag position above head
    local tagCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z + tagHeight)
    
    -- Calculate scale based on distance
    local scale = Config.TagScale * (1.0 - (distance / Config.TagDistance) * 0.5)
    scale = math.max(scale, 0.2)
    
    -- Draw background if enabled
    if tagConfig.background then
        DrawText3DWithBackground(
            tagCoords.x, tagCoords.y, tagCoords.z,
            tagConfig.text,
            scale,
            tagConfig.color,
            tagConfig.backgroundColor
        )
    else
        DrawText3D(
            tagCoords.x, tagCoords.y, tagCoords.z,
            tagConfig.text,
            scale,
            tagConfig.color
        )
    end
end

-- Draw 3D text
function DrawText3D(x, y, z, text, scale, color)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    
    if onScreen then
        SetTextScale(scale, scale)
        SetTextFont(Config.TagFont)
        SetTextProportional(1)
        SetTextColour(color[1], color[2], color[3], color[4])
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Draw 3D text with background
function DrawText3DWithBackground(x, y, z, text, scale, color, bgColor)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    
    if onScreen then
        -- Calculate text dimensions
        SetTextFont(Config.TagFont)
        SetTextScale(scale, scale)
        SetTextEntry("STRING")
        AddTextComponentString(text)
        local textWidth = GetTextScreenWidth(false)
        local textHeight = GetTextScreenHeight(scale, Config.TagFont)
        
        -- Draw background rectangle
        local bgWidth = textWidth + 0.02
        local bgHeight = textHeight + 0.01
        DrawRect(_x, _y, bgWidth, bgHeight, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
        
        -- Draw text
        SetTextScale(scale, scale)
        SetTextFont(Config.TagFont)
        SetTextProportional(1)
        SetTextColour(color[1], color[2], color[3], color[4])
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Event handlers
RegisterNetEvent('tagsystem:updatePlayerTag')
AddEventHandler('tagsystem:updatePlayerTag', function(playerId, tagData)
    if tagData then
        playerTags[playerId] = tagData
    else
        playerTags[playerId] = nil
    end
end)

RegisterNetEvent('tagsystem:updateAllTags')
AddEventHandler('tagsystem:updateAllTags', function(allTags)
    playerTags = allTags
end)

RegisterNetEvent('tagsystem:tagSet')
AddEventHandler('tagsystem:tagSet', function(tagId)
    if tagId == 0 then
        myTag = nil
        Notify("Tag entfernt")
    else
        myTag = tagId
        local tagConfig = Config.TagTypes[tagId]
        if tagConfig then
            Notify("Tag gesetzt: " .. tagConfig.name)
        end
    end
end)

RegisterNetEvent('tagsystem:tagError')
AddEventHandler('tagsystem:tagError', function(message)
    Notify("Fehler: " .. message)
end)

-- Utility function for notifications
function Notify(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)
end

-- Clean up when player leaves
AddEventHandler('playerDropped', function(playerId)
    playerTags[playerId] = nil
end)
