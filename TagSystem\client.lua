local playerTags = {} -- Store all player tags
local myTag = nil -- Current player's tag
local animationOffset = 0.0 -- For floating animation

-- Initialize
Citizen.CreateThread(function()
    -- Request tag data from server
    TriggerServerEvent('tagsystem:requestPlayerTags')

    while true do
        Citizen.Wait(0)

        -- Update animation
        if Config.EnableAnimation then
            animationOffset = math.sin(GetGameTimer() / 1000.0 * Config.AnimationSpeed) * Config.AnimationHeight
        end

        -- Draw all player tags
        DrawAllPlayerTags()
    end
end)

-- Debug variables
local debugActive = false
local debugEndTime = 0

-- Debug command to test tag visibility
RegisterCommand('tagdebug', function()
    debugActive = true
    debugEndTime = GetGameTimer() + 10000 -- 10 seconds
    Notify("Debug: Test-Tag für 10 Sekunden aktiviert")
end, false)

-- Debug command to stop test
RegisterCommand('tagdebugstop', function()
    debugActive = false
    Notify("Debug: Test-Tag deaktiviert")
end, false)

-- Simple test command
RegisterCommand('tagtest', function()
    if myTag then
        Notify("Aktueller Tag: " .. Config.TagTypes[myTag].name)
    else
        Notify("Kein Tag gesetzt")
    end

    -- Show debug info
    local playerCount = 0
    for _ in pairs(playerTags) do
        playerCount = playerCount + 1
    end
    Notify("Spieler mit Tags: " .. playerCount)
end, false)

-- Register tag command
RegisterCommand(Config.TagCommand, function(source, args)
    if args[1] then
        local tagId = tonumber(args[1])
        if tagId and tagId >= 1 and tagId <= #Config.TagTypes then
            TriggerServerEvent('tagsystem:setTag', tagId)
        else
            ShowTagMenu()
        end
    else
        ShowTagMenu()
    end
end, false)

-- Show tag selection menu
function ShowTagMenu()
    local menuText = "~b~Tag System~w~\n\nVerfügbare Tags:\n"
    
    for i, tag in ipairs(Config.TagTypes) do
        menuText = menuText .. string.format("~y~%d~w~. %s\n", i, tag.name)
    end
    
    menuText = menuText .. "\n~r~0~w~. Tag entfernen\n"
    menuText = menuText .. "\nVerwendung: /" .. Config.TagCommand .. " [nummer]"
    
    -- Show notification with menu
    SetNotificationTextEntry("STRING")
    AddTextComponentString(menuText)
    DrawNotification(false, false)
    
    -- Also show in chat
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"Tag System", menuText}
    })
end

-- Draw all player tags
function DrawAllPlayerTags()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local myServerId = GetPlayerServerId(PlayerId())

    -- Draw debug tag if active
    if debugActive then
        if GetGameTimer() > debugEndTime then
            debugActive = false
            Notify("Debug: Test-Tag beendet")
        else
            DrawText3DSimple(playerCoords.x, playerCoords.y, playerCoords.z + 2.0, "★ DEBUG TEST ★", 1.0, {255, 255, 0, 255})
        end
    end

    -- Draw my own tag if I have one
    if myTag then
        local myTagData = { tagId = myTag }
        DrawPlayerTag(playerPed, myTagData, 0)
    end

    -- Draw other players' tags
    for i = 0, 255 do
        if i ~= PlayerId() then
            local targetPed = GetPlayerPed(i)
            local targetServerId = GetPlayerServerId(i)

            if targetPed and targetPed ~= 0 and DoesEntityExist(targetPed) and targetServerId ~= -1 then
                local tagData = playerTags[targetServerId]

                if tagData then
                    local targetCoords = GetEntityCoords(targetPed)
                    local distance = #(playerCoords - targetCoords)

                    if distance <= Config.TagDistance then
                        DrawPlayerTag(targetPed, tagData, distance)
                    end
                end
            end
        end
    end
end

-- Draw individual player tag
function DrawPlayerTag(ped, tagData, distance)
    local tagConfig = Config.TagTypes[tagData.tagId]
    if not tagConfig then return end

    local pedCoords = GetEntityCoords(ped)
    local tagHeight = Config.TagHeight + animationOffset

    -- Calculate tag position above head
    local tagCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z + tagHeight)

    -- Calculate scale based on distance
    local scale = Config.TagScale
    if distance > 0 then
        scale = Config.TagScale * (1.0 - (distance / Config.TagDistance) * 0.3)
        scale = math.max(scale, 0.3)
    end

    -- Use simple 3D text drawing
    DrawText3DSimple(tagCoords.x, tagCoords.y, tagCoords.z, tagConfig.text, scale, tagConfig.color)
end

-- Simple and reliable 3D text function
function DrawText3DSimple(x, y, z, text, scale, color)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)

    if onScreen then
        -- First draw background rectangle
        local bgWidth = string.len(text) * scale * 0.01
        local bgHeight = scale * 0.02
        DrawRect(_x, _y, bgWidth, bgHeight, 0, 0, 0, 150)

        -- Then draw text
        SetTextScale(scale, scale)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(color[1], color[2], color[3], color[4] or 255)
        SetTextOutline()
        SetTextDropShadow()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Legacy functions removed - using DrawText3DSimple instead

-- Event handlers
RegisterNetEvent('tagsystem:updatePlayerTag')
AddEventHandler('tagsystem:updatePlayerTag', function(playerId, tagData)
    if tagData then
        playerTags[playerId] = tagData
    else
        playerTags[playerId] = nil
    end
end)

RegisterNetEvent('tagsystem:updateAllTags')
AddEventHandler('tagsystem:updateAllTags', function(allTags)
    playerTags = allTags
end)

RegisterNetEvent('tagsystem:tagSet')
AddEventHandler('tagsystem:tagSet', function(tagId)
    if tagId == 0 then
        myTag = nil
        Notify("Tag entfernt")
    else
        myTag = tagId
        local tagConfig = Config.TagTypes[tagId]
        if tagConfig then
            Notify("Tag gesetzt: " .. tagConfig.name)
        end
    end
end)

RegisterNetEvent('tagsystem:tagError')
AddEventHandler('tagsystem:tagError', function(message)
    Notify("Fehler: " .. message)
end)

-- Utility function for notifications
function Notify(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)
end

-- Clean up when player leaves
AddEventHandler('playerDropped', function(playerId)
    playerTags[playerId] = nil
end)
