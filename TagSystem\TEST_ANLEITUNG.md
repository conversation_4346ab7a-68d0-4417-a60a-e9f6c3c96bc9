# Tag System - Test Anleitung

## 🔧 Fehlerbehebung: Tags nicht sichtbar

### **Problem behoben:**
1. **Player-ID Mapping korrigiert** - System verwendet jetzt korrekte Client/Server-ID Zuordnung
2. **Eigener Tag wird angezeigt** - Sie können jetzt Ihren eigenen Tag sehen
3. **Verbesserte 3D-Text-Funktion** - Neue, zuverlässige Rendering-Methode
4. **Debug-Funktionen hinzugefügt** - Zum Testen der Tag-Sichtbarkeit

## 🧪 **Sofort-Test:**

### **Schritt 1: Debug-Test**
```
/tagdebug
```
- Zeigt einen gelben Test-Tag für 5 Sekunden über Ihrem Kopf
- Wenn dieser sichtbar ist, funktioniert das 3D-Rendering

### **Schritt 2: Tag setzen**
```
/tag 1
```
- Setzt VIP-Tag (★ VIP ★ in Gold)
- Sollte sofort über Ihrem Kopf erscheinen

### **Schritt 3: Tag-Menü öffnen**
```
/tag
```
- Zeigt alle verfügbaren Tags
- Wählen Sie eine Nummer von 1-6

## 🎯 **Verfügbare Test-Tags:**

1. **VIP** - ★ VIP ★ (Gold)
2. **Admin** - ⚡ ADMIN ⚡ (Rot)
3. **Moderator** - 🛡️ MOD 🛡️ (Grün)
4. **Player** - 👤 PLAYER (Weiß)
5. **Streamer** - 📺 LIVE (Magenta)
6. **Developer** - 💻 DEV (Cyan)

## 🔍 **Wenn Tags immer noch nicht sichtbar sind:**

### **Überprüfung 1: Resource läuft**
```
/refresh
/ensure TagSystem
```

### **Überprüfung 2: Konsole-Fehler**
- Drücken Sie F8 im Spiel
- Schauen Sie nach Fehlermeldungen
- Suchen Sie nach "TagSystem" Einträgen

### **Überprüfung 3: Andere Spieler**
- Bitten Sie einen anderen Spieler, ein Tag zu setzen
- Schauen Sie, ob Sie deren Tag sehen können
- Tags sind bis zu 100m Entfernung sichtbar

## ⚙️ **Verbesserte Einstellungen:**

### **Neue Features:**
- **Automatische Skalierung** basierend auf Entfernung
- **Schwarzer Hintergrund** für bessere Lesbarkeit
- **Schatten-Effekte** für mehr Kontrast
- **Optimierte Höhe** (1.0m über dem Kopf)
- **Größere Sichtweite** (100m statt 50m)

### **Debug-Informationen:**
```
/tagdebug    - Test-Tag für 5 Sekunden
/tag         - Tag-Menü anzeigen
/tag 0       - Tag entfernen
/listtags    - Alle verfügbaren Tags
```

## 🚨 **Häufige Probleme:**

### **Problem: "Tag gesetzt" aber nicht sichtbar**
**Lösung:**
1. Führen Sie `/tagdebug` aus
2. Wenn Test-Tag sichtbar ist: Server neu starten
3. Wenn Test-Tag nicht sichtbar: Resource-Problem

### **Problem: Nur eigener Tag nicht sichtbar**
**Lösung:**
- Das ist normal - schauen Sie in einen Spiegel oder bitten Sie andere Spieler um Bestätigung

### **Problem: Tags zu klein/groß**
**Lösung:**
- Bearbeiten Sie `Config.TagScale` in `config.lua`
- Standard: 0.8 (empfohlen: 0.5 - 1.2)

## 📋 **Erfolgreicher Test-Ablauf:**

1. ✅ `/tagdebug` zeigt gelben Test-Tag
2. ✅ `/tag 1` setzt VIP-Tag
3. ✅ Tag ist über dem Kopf sichtbar
4. ✅ Tag bewegt sich mit dem Spieler
5. ✅ Tag hat schwarzen Hintergrund
6. ✅ Tag ist aus der Entfernung sichtbar

## 🔧 **Bei anhaltenden Problemen:**

### **Server-Neustart:**
```
/restart TagSystem
```

### **Vollständiger Reset:**
```
/stop TagSystem
/refresh
/ensure TagSystem
```

### **Alternative Test-Methode:**
Wenn das normale Tag-System nicht funktioniert, können Sie den Debug-Befehl verwenden:
```lua
-- In der F8-Konsole:
DrawText3DSimple(GetEntityCoords(PlayerPedId()).x, GetEntityCoords(PlayerPedId()).y, GetEntityCoords(PlayerPedId()).z + 2.0, "TEST", 1.0, {255, 255, 255, 255})
```

## 📞 **Support:**
Falls die Tags immer noch nicht sichtbar sind, überprüfen Sie:
1. FiveM Client-Version (aktuell?)
2. Andere Scripts die 3D-Text verwenden
3. Grafiktreiber-Einstellungen
4. FiveM Grafikeinstellungen
