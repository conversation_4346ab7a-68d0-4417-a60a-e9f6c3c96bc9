@echo off
echo FiveM Resource Fix Script
echo ========================

echo.
echo Checking for common FiveM resource issues...

REM Check if server.cfg exists
if not exist "server.cfg" (
    echo [ERROR] server.cfg not found!
    echo Creating basic server.cfg...
    echo # Basic FiveM Server Configuration > server.cfg
    echo endpoint_add_tcp "0.0.0.0:30120" >> server.cfg
    echo endpoint_add_udp "0.0.0.0:30120" >> server.cfg
    echo sv_hostname "FiveM Server" >> server.cfg
    echo sv_maxclients 32 >> server.cfg
    echo sv_licenseKey "changeme" >> server.cfg
    echo set onesync on >> server.cfg
    echo ensure mapmanager >> server.cfg
    echo ensure chat >> server.cfg
    echo ensure spawnmanager >> server.cfg
    echo ensure sessionmanager >> server.cfg
    echo ensure basic-gamemode >> server.cfg
    echo ensure hardcap >> server.cfg
    echo [FIXED] Basic server.cfg created
)

REM Check for Koro resource
if not exist "Koro\fxmanifest.lua" (
    echo [ERROR] Koro resource missing fxmanifest.lua
    if not exist "Koro" mkdir Koro
    echo fx_version 'cerulean' > Koro\fxmanifest.lua
    echo game 'gta5' >> Koro\fxmanifest.lua
    echo author 'Koro' >> Koro\fxmanifest.lua
    echo description 'Koro Resource' >> Koro\fxmanifest.lua
    echo version '1.0.0' >> Koro\fxmanifest.lua
    echo [FIXED] Koro resource created
)

REM Check other resources
echo.
echo Checking resource manifests...

for /d %%i in (*) do (
    if exist "%%i" (
        if not exist "%%i\fxmanifest.lua" (
            if not exist "%%i\__resource.lua" (
                echo [WARNING] %%i has no manifest file
            )
        ) else (
            echo [OK] %%i has fxmanifest.lua
        )
    )
)

echo.
echo Resource check complete!
echo.
echo Next steps:
echo 1. Update your sv_licenseKey in server.cfg
echo 2. Remove any "ensure Koro" lines from server.cfg if not needed
echo 3. Start your FiveM server
echo.
pause
