local playerTags = {} -- Store all player tags server-side

-- Initialize player when they join
AddEventHandler('playerConnecting', function()
    local playerId = source
    -- Set default tag if configured
    if Config.DefaultTag and Config.DefaultTag > 0 then
        playerTags[playerId] = {
            tagId = Config.DefaultTag,
            timestamp = os.time()
        }
    end
end)

-- Clean up when player leaves
AddEventHandler('playerDropped', function()
    local playerId = source
    playerTags[playerId] = nil
    
    -- Notify all clients to remove this player's tag
    TriggerClientEvent('tagsystem:updatePlayerTag', -1, playerId, nil)
end)

-- Handle tag setting request
RegisterServerEvent('tagsystem:setTag')
AddEventHandler('tagsystem:setTag', function(tagId)
    local playerId = source
    
    -- Validate tag ID
    if tagId == 0 then
        -- Remove tag
        playerTags[playerId] = nil
        TriggerClientEvent('tagsystem:tagSet', playerId, 0)
        TriggerClientEvent('tagsystem:updatePlayerTag', -1, playerId, nil)
        print(string.format("Player %d removed their tag", playerId))
        return
    end
    
    if not tagId or tagId < 1 or tagId > #Config.TagTypes then
        TriggerClientEvent('tagsystem:tagError', playerId, "Ungültige Tag-ID")
        return
    end
    
    -- Check permissions if required
    if Config.RequirePermission then
        local permission = Config.TagPermissions[tagId]
        if permission and not IsPlayerAceAllowed(playerId, permission) then
            TriggerClientEvent('tagsystem:tagError', playerId, "Keine Berechtigung für diesen Tag")
            return
        end
    end
    
    -- Set the tag
    local tagData = {
        tagId = tagId,
        timestamp = os.time()
    }
    
    playerTags[playerId] = tagData
    
    -- Notify the player
    TriggerClientEvent('tagsystem:tagSet', playerId, tagId)
    
    -- Update all clients
    TriggerClientEvent('tagsystem:updatePlayerTag', -1, playerId, tagData)
    
    local tagConfig = Config.TagTypes[tagId]
    print(string.format("Player %d set tag to: %s (%s)", playerId, tagConfig.name, tagConfig.text))
end)

-- Handle request for all player tags
RegisterServerEvent('tagsystem:requestPlayerTags')
AddEventHandler('tagsystem:requestPlayerTags', function()
    local playerId = source
    
    -- Send all current tags to the requesting player
    TriggerClientEvent('tagsystem:updateAllTags', playerId, playerTags)
end)

-- Admin command to set tag for another player
RegisterCommand('settag', function(source, args, rawCommand)
    if source == 0 then -- Console command
        if #args < 2 then
            print("Usage: settag <playerId> <tagId>")
            return
        end
        
        local targetId = tonumber(args[1])
        local tagId = tonumber(args[2])
        
        if not targetId or not GetPlayerName(targetId) then
            print("Invalid player ID")
            return
        end
        
        if tagId == 0 then
            -- Remove tag
            playerTags[targetId] = nil
            TriggerClientEvent('tagsystem:tagSet', targetId, 0)
            TriggerClientEvent('tagsystem:updatePlayerTag', -1, targetId, nil)
            print(string.format("Removed tag from player %d", targetId))
        elseif tagId and tagId >= 1 and tagId <= #Config.TagTypes then
            local tagData = {
                tagId = tagId,
                timestamp = os.time()
            }
            
            playerTags[targetId] = tagData
            TriggerClientEvent('tagsystem:tagSet', targetId, tagId)
            TriggerClientEvent('tagsystem:updatePlayerTag', -1, targetId, tagData)
            
            local tagConfig = Config.TagTypes[tagId]
            print(string.format("Set tag for player %d to: %s", targetId, tagConfig.name))
        else
            print("Invalid tag ID. Available tags: 1-" .. #Config.TagTypes)
        end
    else
        -- Player command - check if they have admin permission
        if IsPlayerAceAllowed(source, "tag.admin") then
            if #args < 2 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    args = {"System", "Usage: /settag <playerId> <tagId>"}
                })
                return
            end
            
            local targetId = tonumber(args[1])
            local tagId = tonumber(args[2])
            
            if not targetId or not GetPlayerName(targetId) then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    args = {"System", "Invalid player ID"}
                })
                return
            end
            
            if tagId == 0 then
                -- Remove tag
                playerTags[targetId] = nil
                TriggerClientEvent('tagsystem:tagSet', targetId, 0)
                TriggerClientEvent('tagsystem:updatePlayerTag', -1, targetId, nil)
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {"System", string.format("Removed tag from player %d", targetId)}
                })
            elseif tagId and tagId >= 1 and tagId <= #Config.TagTypes then
                local tagData = {
                    tagId = tagId,
                    timestamp = os.time()
                }
                
                playerTags[targetId] = tagData
                TriggerClientEvent('tagsystem:tagSet', targetId, tagId)
                TriggerClientEvent('tagsystem:updatePlayerTag', -1, targetId, tagData)
                
                local tagConfig = Config.TagTypes[tagId]
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {"System", string.format("Set tag for player %d to: %s", targetId, tagConfig.name)}
                })
            else
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    args = {"System", "Invalid tag ID. Available tags: 1-" .. #Config.TagTypes}
                })
            end
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {"System", "You don't have permission to use this command"}
            })
        end
    end
end, false)

-- Command to list all available tags
RegisterCommand('listtags', function(source, args, rawCommand)
    local message = "Available Tags:\n"
    
    for i, tag in ipairs(Config.TagTypes) do
        message = message .. string.format("%d. %s - %s\n", i, tag.name, tag.text)
    end
    
    if source == 0 then
        print(message)
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 255, 255},
            multiline = true,
            args = {"Tag System", message}
        })
    end
end, false)

-- Save tags periodically (optional - for persistence)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(300000) -- Save every 5 minutes
        
        -- Here you could save playerTags to a database
        -- For now, we'll just print the count
        local count = 0
        for _ in pairs(playerTags) do
            count = count + 1
        end
        
        if count > 0 then
            print(string.format("Tag System: %d players currently have tags", count))
        end
    end
end)
