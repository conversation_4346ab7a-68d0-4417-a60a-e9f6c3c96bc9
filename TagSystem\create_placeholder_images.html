<!DOCTYPE html>
<html>
<head>
    <title>Tag Image Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .canvas-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .download-btn { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            cursor: pointer; 
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>FiveM Tag System - Bild Generator</h1>
    <p>Klicken Sie auf die Buttons um Platzhalter-Bilder zu erstellen:</p>
    
    <div class="canvas-container">
        <canvas id="vip" width="512" height="512"></canvas>
        <button class="download-btn" onclick="downloadImage('vip', 'vip_tag.png')">VIP Tag herunterladen</button>
    </div>
    
    <div class="canvas-container">
        <canvas id="admin" width="512" height="512"></canvas>
        <button class="download-btn" onclick="downloadImage('admin', 'admin_tag.png')">Admin Tag herunterladen</button>
    </div>
    
    <div class="canvas-container">
        <canvas id="mod" width="512" height="512"></canvas>
        <button class="download-btn" onclick="downloadImage('mod', 'mod_tag.png')">Mod Tag herunterladen</button>
    </div>
    
    <div class="canvas-container">
        <canvas id="streamer" width="512" height="512"></canvas>
        <button class="download-btn" onclick="downloadImage('streamer', 'streamer_tag.png')">Streamer Tag herunterladen</button>
    </div>
    
    <div class="canvas-container">
        <canvas id="dev" width="512" height="512"></canvas>
        <button class="download-btn" onclick="downloadImage('dev', 'dev_tag.png')">Dev Tag herunterladen</button>
    </div>

    <script>
        // Tag configurations
        const tags = {
            vip: { text: 'VIP', color: '#FFD700', bgColor: '#000000', symbol: '★' },
            admin: { text: 'ADMIN', color: '#FF0000', bgColor: '#000000', symbol: '⚡' },
            mod: { text: 'MOD', color: '#00FF00', bgColor: '#000000', symbol: '🛡️' },
            streamer: { text: 'LIVE', color: '#FF00FF', bgColor: '#000000', symbol: '📺' },
            dev: { text: 'DEV', color: '#00FFFF', bgColor: '#000000', symbol: '💻' }
        };

        // Create images
        function createTagImage(canvasId, config) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background
            ctx.fillStyle = config.bgColor + '99'; // Semi-transparent
            ctx.fillRect(50, 200, 412, 112);
            
            // Border
            ctx.strokeStyle = config.color;
            ctx.lineWidth = 4;
            ctx.strokeRect(50, 200, 412, 112);
            
            // Symbol
            ctx.font = 'bold 80px Arial';
            ctx.fillStyle = config.color;
            ctx.textAlign = 'center';
            ctx.fillText(config.symbol, 130, 280);
            
            // Text
            ctx.font = 'bold 60px Arial';
            ctx.fillText(config.text, 320, 280);
        }

        // Download function
        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // Generate all images on load
        window.onload = function() {
            for (const [id, config] of Object.entries(tags)) {
                createTagImage(id, config);
            }
        };
    </script>
</body>
</html>
