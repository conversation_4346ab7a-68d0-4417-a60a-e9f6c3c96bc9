<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Image Creator - TagSystem</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 40px;
        }
        
        .method-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #007bff;
        }
        
        .method-title {
            color: #007bff;
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f0f8ff;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e6f3ff;
            border-color: #0056b3;
        }
        
        .upload-area.dragover {
            background: #cce7ff;
            border-color: #0056b3;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        
        .preview-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .preview-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .preview-card:hover {
            transform: translateY(-5px);
        }
        
        .preview-img {
            max-width: 100%;
            max-height: 150px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: scale(1.05);
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .tag-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .tag-type {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Custom Image Creator</h1>
            <p>Erstellen Sie eigene Bilder für Ihr TagSystem</p>
        </div>
        
        <div class="content">
            <!-- Methode 1: Drag & Drop Upload -->
            <div class="method-section">
                <div class="method-title">📁 Methode 1: Bilder hochladen und konvertieren</div>
                
                <div class="upload-area" id="uploadArea">
                    <h3>🖼️ Bilder hier hineinziehen oder klicken zum Auswählen</h3>
                    <p>Unterstützte Formate: PNG, JPG, JPEG</p>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Bilder auswählen
                    </button>
                    <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
                </div>
                
                <div class="instructions">
                    <strong>📋 Anweisungen:</strong>
                    <ol>
                        <li>Laden Sie Ihre Bilder hoch (PNG/JPG, optimal 512x512 Pixel)</li>
                        <li>Benennen Sie sie entsprechend um: admin_tag.png, vip_tag.png, etc.</li>
                        <li>Laden Sie die konvertierten Bilder herunter</li>
                        <li>Legen Sie sie in den <code>TagSystem/images/</code> Ordner</li>
                        <li>Starten Sie die Resource neu: <code>/restart TagSystem</code></li>
                    </ol>
                </div>
                
                <div class="preview-area" id="previewArea"></div>
            </div>
            
            <!-- Tag-Typen -->
            <div class="method-section">
                <div class="method-title">🏷️ Verfügbare Tag-Typen</div>
                <div class="tag-types">
                    <div class="tag-type">admin_tag.png<br>Admin</div>
                    <div class="tag-type">vip_tag.png<br>VIP</div>
                    <div class="tag-type">mod_tag.png<br>Moderator</div>
                    <div class="tag-type">streamer_tag.png<br>Streamer</div>
                    <div class="tag-type">dev_tag.png<br>Developer</div>
                </div>
            </div>
            
            <!-- Methode 2: Code-Generator -->
            <div class="method-section">
                <div class="method-title">⚙️ Methode 2: Neuen Tag-Typ hinzufügen</div>
                
                <label for="tagName">Tag-Name:</label>
                <input type="text" id="tagName" placeholder="z.B. Supporter" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                
                <label for="tagId">Tag-ID:</label>
                <input type="number" id="tagId" placeholder="z.B. 6" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                
                <button onclick="generateCode()" class="upload-btn">Code generieren</button>
                
                <div id="generatedCode" style="margin-top: 20px;"></div>
            </div>
            
            <!-- Methode 3: Anleitung -->
            <div class="method-section">
                <div class="method-title">📖 Methode 3: Manuelle Installation</div>
                
                <h4>Datei-Struktur:</h4>
                <div class="code-block">
TagSystem/
├── images/                 # Ihre PNG/JPG Bilder hier
│   ├── admin_tag.png
│   ├── vip_tag.png
│   ├── mod_tag.png
│   ├── streamer_tag.png
│   └── dev_tag.png
├── stream/                 # YTD-Dateien (optional)
├── config.lua
├── client.lua
└── fxmanifest.lua
                </div>
                
                <h4>Test-Befehle:</h4>
                <div class="code-block">
/restart TagSystem          # Resource neu starten
/tag 1                      # VIP Tag testen
/tag 2                      # Admin Tag testen
/tagimagetest 2             # 10 Sekunden Test
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Drag & Drop Funktionalität
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewArea = document.getElementById('previewArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            previewArea.innerHTML = '';
            
            Array.from(files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        createPreviewCard(e.target.result, file.name, index);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
        
        function createPreviewCard(src, originalName, index) {
            const card = document.createElement('div');
            card.className = 'preview-card';
            
            const tagNames = ['admin_tag.png', 'vip_tag.png', 'mod_tag.png', 'streamer_tag.png', 'dev_tag.png'];
            const suggestedName = tagNames[index] || `custom_tag_${index + 1}.png`;
            
            card.innerHTML = `
                <img src="${src}" class="preview-img" alt="Preview">
                <h4>Original: ${originalName}</h4>
                <p>Vorschlag: <strong>${suggestedName}</strong></p>
                <button class="download-btn" onclick="downloadImage('${src}', '${suggestedName}')">
                    Als ${suggestedName} herunterladen
                </button>
            `;
            
            previewArea.appendChild(card);
        }
        
        function downloadImage(src, filename) {
            const link = document.createElement('a');
            link.href = src;
            link.download = filename;
            link.click();
        }
        
        function generateCode() {
            const tagName = document.getElementById('tagName').value;
            const tagId = document.getElementById('tagId').value;
            
            if (!tagName || !tagId) {
                alert('Bitte füllen Sie alle Felder aus!');
                return;
            }
            
            const imageName = tagName.toLowerCase().replace(/\s+/g, '_') + '_tag.png';
            
            const configCode = `
-- Fügen Sie dies zu Ihrer config.lua hinzu:
{
    id = ${tagId},
    name = "${tagName}",
    type = "image",
    image = "${imageName}",
    text = "${tagName.toUpperCase()}",
    size = {0.08, 0.08},
    color = {255, 255, 255}
}`;
            
            const clientCode = `
-- Fügen Sie dies zu client.lua hinzu (in DrawSprite3D Funktion):
elseif imageName == "${imageName}" then
    Draw${tagName}Tag(_x, _y, width, height)

-- Und diese Funktion am Ende der Datei:
function Draw${tagName}Tag(x, y, width, height)
    -- Ihr Custom Design hier
    DrawRect(x, y, width, height, 100, 100, 255, 200) -- Beispiel: Blauer Hintergrund
end`;
            
            document.getElementById('generatedCode').innerHTML = `
                <h4>Config.lua Code:</h4>
                <div class="code-block">${configCode}</div>
                <h4>Client.lua Code:</h4>
                <div class="code-block">${clientCode}</div>
                <div class="instructions">
                    <strong>📁 Vergessen Sie nicht:</strong> Erstellen Sie die Bilddatei <code>${imageName}</code> und legen Sie sie in den <code>images/</code> Ordner!
                </div>
            `;
        }
    </script>
</body>
</html>
