# 🎨 Custom Images für Tag System

## 🚀 **Schnellstart:**

Das Tag-System unterstützt jetzt echte Bilder anstelle von Text-Symbolen!

### **Schritt 1: Platzhalter-Bilder erstellen**
1. <PERSON><PERSON><PERSON>e `create_placeholder_images.html` in Ihrem Browser
2. Klicken Sie auf die Download-Buttons für jedes Tag
3. Speichern Sie die Bilder im `TagSystem/images/` Ordner

### **Schritt 2: Resource neu starten**
```
/restart TagSystem
```

### **Schritt 3: Testen**
```
/tag 1    # VIP Tag mit Bild
/tag 2    # Admin Tag mit Bild
/tag 3    # Moderator Tag mit Bild
```

## 🎯 **Custom-Bilder erstellen:**

### **Empfohlene Spezifikationen:**
- **Format:** PNG (mit Transparenz)
- **Größe:** 512x512 Pixel
- **Design:** Einfach und kontrastreich
- **Dateigröße:** Unter 1MB

### **Design-Tipps:**
1. **Kontrast:** Verwenden Sie helle Farben auf dunklem Hintergrund
2. **Einfachheit:** Komplexe Details sind aus der Entfernung nicht sichtbar
3. **Transparenz:** PNG mit Alpha-Kanal für bessere Integration
4. **Größe:** Nicht zu detailliert, da Tags klein dargestellt werden

## 🔧 **Konfiguration anpassen:**

### **Neue Tags hinzufügen:**
```lua
{
    id = 7,
    name = "Custom",
    type = "image",
    image = "custom_tag.png",
    text = "CUSTOM", -- Fallback
    size = {0.08, 0.08}, -- Breite, Höhe
    background = false -- Optional
}
```

### **Tag-Größe anpassen:**
```lua
size = {0.12, 0.12} -- Größer
size = {0.06, 0.06} -- Kleiner
```

### **Zwischen Bild und Text wechseln:**
```lua
type = "image" -- Für Bilder
type = "text"  -- Für Text
```

## 🎨 **Beispiel-Designs:**

### **VIP Tag:**
- Goldener Stern mit "VIP" Text
- Eleganter Rahmen
- Transparenter Hintergrund

### **Admin Tag:**
- Roter Blitz mit "ADMIN" Text
- Kraftvolles Design
- Warnsignal-Optik

### **Moderator Tag:**
- Grünes Schild mit "MOD" Text
- Schutz-Symbol
- Vertrauenserweckend

## 🔍 **Fehlerbehebung:**

### **Rote Platzhalter erscheinen:**
- Überprüfen Sie den Dateinamen (Groß-/Kleinschreibung)
- Stellen Sie sicher, dass das Bild im `images/` Ordner liegt
- Überprüfen Sie das Dateiformat (PNG/JPG/DDS)

### **Bilder werden nicht geladen:**
- Resource neu starten: `/restart TagSystem`
- F8-Konsole auf Fehlermeldungen überprüfen
- Dateigröße überprüfen (unter 1MB)

### **Bilder zu groß/klein:**
- `size` Parameter in config.lua anpassen
- Werte zwischen 0.04 und 0.15 empfohlen

## 🎮 **Test-Befehle:**

```
/tagdebug     # Test-Tag anzeigen
/tag 1        # VIP Bild-Tag
/tag 4        # Player Text-Tag (Vergleich)
/tagtest      # Status anzeigen
```

## 📁 **Datei-Struktur:**

```
TagSystem/
├── images/
│   ├── vip_tag.png
│   ├── admin_tag.png
│   ├── mod_tag.png
│   ├── streamer_tag.png
│   └── dev_tag.png
├── config.lua
├── client.lua
└── fxmanifest.lua
```

## 🎯 **Erweiterte Anpassungen:**

### **Animierte Tags:**
- Verwenden Sie mehrere Bilder
- Wechseln Sie zwischen ihnen in der Render-Schleife
- Erstellen Sie Blink- oder Fade-Effekte

### **Bedingte Tags:**
- Verschiedene Bilder basierend auf Tageszeit
- Spezielle Event-Tags
- Rang-basierte Variationen

### **Performance-Optimierung:**
- Verwenden Sie DDS-Format für bessere Performance
- Komprimieren Sie PNG-Dateien
- Laden Sie nur benötigte Texturen

## 💡 **Kreative Ideen:**

1. **Clan-Tags:** Logo Ihres Clans/Servers
2. **Event-Tags:** Spezielle Tags für Events
3. **Rang-System:** Verschiedene Ränge mit eigenen Bildern
4. **Thematische Tags:** Halloween, Weihnachten, etc.
5. **Sponsor-Tags:** Partner-Logos (mit Erlaubnis)

Das System ist sehr flexibel - experimentieren Sie mit verschiedenen Designs und Größen!
