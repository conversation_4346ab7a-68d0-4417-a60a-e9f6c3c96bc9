<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TagSystem Image Loader</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: Arial, sans-serif;
        }
        
        .hidden {
            display: none;
        }
        
        .image-container {
            position: absolute;
            pointer-events: none;
        }
        
        .tag-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        #imagePreloader {
            position: absolute;
            top: -9999px;
            left: -9999px;
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="imagePreloader"></div>
    
    <script>
        let loadedImages = {};
        let imageElements = {};
        
        // Preload images
        function preloadImage(imageName) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    loadedImages[imageName] = true;
                    imageElements[imageName] = img;
                    console.log('Loaded image:', imageName);
                    resolve(img);
                };
                img.onerror = () => {
                    console.log('Failed to load image:', imageName);
                    loadedImages[imageName] = false;
                    reject();
                };
                img.src = `../images/${imageName}`;
            });
        }
        
        // Initialize and preload all tag images
        async function initializeImages() {
            const imageNames = [
                'admin_tag.png',
                'vip_tag.png', 
                'mod_tag.png',
                'streamer_tag.png',
                'dev_tag.png'
            ];
            
            for (const imageName of imageNames) {
                try {
                    await preloadImage(imageName);
                } catch (e) {
                    console.log(`Image ${imageName} not found, using fallback`);
                }
            }
            
            // Notify client that images are loaded
            if (window.invokeNative) {
                window.postMessage({
                    type: 'imagesLoaded',
                    loadedImages: loadedImages
                }, '*');
            }
        }
        
        // Handle messages from client
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            if (data.type === 'checkImage') {
                const imageName = data.imageName;
                const exists = loadedImages[imageName] === true;
                
                window.postMessage({
                    type: 'imageExists',
                    imageName: imageName,
                    exists: exists
                }, '*');
            }
            
            if (data.type === 'getImageData') {
                const imageName = data.imageName;
                if (imageElements[imageName]) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = imageElements[imageName];
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    const imageData = canvas.toDataURL();
                    
                    window.postMessage({
                        type: 'imageData',
                        imageName: imageName,
                        data: imageData,
                        width: img.width,
                        height: img.height
                    }, '*');
                }
            }
        });
        
        // Start loading images when page loads
        document.addEventListener('DOMContentLoaded', initializeImages);
        
        // Also try to load immediately
        initializeImages();
    </script>
</body>
</html>
