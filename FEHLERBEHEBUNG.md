# FiveM Fehlerbehebung - "Couldn't load resource Koro"

## 🚨 Problem
Der <PERSON>hler "Couldn't load resource Koro: Couldn't load resource Koro from resources/Koro/: Could not open resource metadata file - no such file." tritt auf.

## ✅ Lösung

### **Schritt 1: Überprüfung der server.cfg**
1. Öffnen Sie Ihre `server.cfg` Datei
2. Suchen Sie nach der Zeile `ensure Koro`
3. **Entfernen oder kommentieren Sie diese Zeile aus:**
   ```
   # ensure Koro  # Auskommentiert
   ```

### **Schritt 2: Korrekte Resource-Struktur**
<PERSON><PERSON><PERSON><PERSON> sic<PERSON>, dass alle Resources die richtige Struktur haben:

```
resources/
├── Adminscript/
│   ├── fxmanifest.lua ✅
│   ├── client.lua
│   └── server.lua
├── TagSystem/
│   ├── fxmanifest.lua ✅
│   ├── client.lua
│   └── server.lua
└── ox_inventory/
    ├── fxmanifest.lua ✅
    └── ...
```

### **Schritt 3: Verwendung der bereitgestellten server.cfg**
Ich habe eine funktionierende `server.cfg` erstellt. Wichtige Punkte:

1. **Lizenzschlüssel aktualisieren:**
   ```
   sv_licenseKey "IHR_ECHTER_LIZENZSCHLÜSSEL"
   ```

2. **Nur funktionierende Resources laden:**
   ```
   ensure ox_inventory
   ensure Adminscript
   ensure Flashbang
   ensure TagSystem
   ```

### **Schritt 4: Resource-Pfade korrigieren**
Für Scripts im `Server_script` Ordner:

**Option A: Resources verschieben**
```bash
# Verschieben Sie die Scripts aus Server_script/ in den Hauptordner
mv "Server_script/doors_creator" "doors_creator"
mv "Server_script/jobs_creator" "jobs_creator"
# etc.
```

**Option B: Pfade in server.cfg anpassen**
```
ensure Server_script/doors_creator
ensure Server_script/jobs_creator
```

## 🔧 Automatische Fehlerbehebung

Führen Sie das bereitgestellte `fix_resources.bat` Script aus:
```cmd
.\fix_resources.bat
```

Das Script:
- ✅ Erstellt eine grundlegende server.cfg falls fehlend
- ✅ Erstellt eine leere Koro-Resource falls erwartet
- ✅ Überprüft alle Resources auf fxmanifest.lua
- ✅ Zeigt Warnungen für problematische Resources

## 📋 Checkliste nach der Fehlerbehebung

- [ ] `server.cfg` enthält gültigen Lizenzschlüssel
- [ ] Keine `ensure Koro` Zeile in server.cfg (außer wenn gewünscht)
- [ ] Alle verwendeten Resources haben `fxmanifest.lua`
- [ ] Resource-Pfade in server.cfg sind korrekt
- [ ] Server startet ohne Fehler

## 🎯 Empfohlene server.cfg für Ihr Setup

```cfg
# Grundlegende Einstellungen
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"
sv_hostname "Ihr Server Name"
sv_maxclients 32
sv_licenseKey "IHR_LIZENZSCHLÜSSEL"
set onesync on

# Basis-Resources
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

# Ihre Custom Resources
ensure ox_inventory
ensure Adminscript
ensure Flashbang
ensure TagSystem

# Tag-System Berechtigungen
add_ace group.admin tag.admin allow
add_ace group.moderator tag.moderator allow
add_ace group.vip tag.vip allow
```

## 🆘 Weitere Hilfe

Falls der Fehler weiterhin auftritt:

1. **Konsole überprüfen:** Schauen Sie in die FiveM Server-Konsole für weitere Fehlermeldungen
2. **Resource-Logs:** Überprüfen Sie die Logs der einzelnen Resources
3. **Abhängigkeiten:** Stellen Sie sicher, dass alle erforderlichen Dependencies installiert sind

## 📞 Support

Bei weiteren Problemen:
- Überprüfen Sie die FiveM Dokumentation
- Kontaktieren Sie den Server-Administrator
- Erstellen Sie ein Backup vor Änderungen
