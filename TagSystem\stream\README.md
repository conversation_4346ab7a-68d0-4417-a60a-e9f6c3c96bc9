# Stream Ordner für YTD-Dateien

## 📁 **Für professionelle Custom Textures**

Legen Sie hier Ihre YTD-Dateien ab für echte Texture-Loading:

### 📋 **Datei-Namenskonvention:**
- `tagsystem_admin_tag.ytd` - Admin Tag Texture
- `tagsystem_vip_tag.ytd` - VIP Tag Texture  
- `tagsystem_mod_tag.ytd` - Moderator Tag Texture
- `tagsystem_streamer_tag.ytd` - Streamer Tag Texture
- `tagsystem_dev_tag.ytd` - Developer Tag Texture

### 🔧 **YTD-Erstellung:**
1. Verwenden Sie OpenIV oder ähnliche Tools
2. Erstellen Sie eine neue YTD-Datei
3. Importieren Sie Ihr PNG/JPG Bild
4. Benennen Sie die Texture entsprechend (ohne "tagsystem_" Prefix)
5. Speichern Sie als YTD-Datei

### 🎯 **Texture-Namen in YTD:**
- Für `tagsystem_admin_tag.ytd` → Texture-Name: `admin_tag`
- Für `tagsystem_vip_tag.ytd` → Texture-Name: `vip_tag`
- etc.

### ⚡ **Automatisches Loading:**
Das System lädt automatisch YTD-Dateien und verwendet sie anstelle der Custom Graphics.

### 🔍 **Fehlerbehebung:**
- Stellen Sie sicher, dass die YTD-Datei korrekt benannt ist
- Überprüfen Sie den Texture-Namen innerhalb der YTD
- Resource neu starten nach Änderungen
- F8-Konsole auf Fehlermeldungen überprüfen
