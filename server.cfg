# FiveM Server Configuration
# Basic server settings
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"

# Server info
sv_hostname "<PERSON><PERSON>'s FiveM Server"
sv_maxclients 32
sv_endpointprivacy true

# License key (replace with your actual key)
sv_licenseKey "changeme"

# Steam Web API key (optional, for Steam integration)
# steam_webApiKey "changeme"

# Database settings (if using MySQL)
# set mysql_connection_string "mysql://user:password@localhost/database"

# OneSync settings
set onesync on

# Server tags
sets tags "roleplay, german, custom"

# Locale
sets locale "de-DE"

# Loading screen
# loadscreen "path/to/your/loadscreen"

# Essential resources
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

# Framework (uncomment if using ESX or QBCore)
# ensure es_extended
# ensure qb-core

# Custom resources
ensure ox_inventory
ensure Adminscript
ensure Flashbang
ensure TagSystem

# Server scripts from Server_script folder (adjust paths as needed)
# ensure Server_script/doors_creator
# ensure Server_script/drugs_creator
# ensure Server_script/farming_creator
# ensure Server_script/jobs_creator
# ensure Server_script/races_creator
# ensure Server_script/robberies_creator
# ensure Server_script/shops_creator

# Note: Uncomment and adjust the above lines based on your actual resource structure

# Admin permissions
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.steam:110000112345678 group.admin  # Replace with your Steam ID

# Moderator permissions
add_ace group.moderator command allow
add_ace group.moderator command.quit deny
add_ace group.moderator command.stop deny

# VIP permissions
add_ace group.vip tag.vip allow

# Tag system permissions
add_ace group.admin tag.admin allow
add_ace group.moderator tag.moderator allow
add_ace group.admin tag.developer allow

# Console commands
add_ace resource.console command allow

# Server convars
set sv_enforceGameBuild 2699
set sv_scriptHookAllowed 0

# Anti-cheat
set sv_enableAntiCheat true

# Voice chat (if using)
# ensure pma-voice

# Restart schedule (optional)
# set sv_endpointprivacy true
