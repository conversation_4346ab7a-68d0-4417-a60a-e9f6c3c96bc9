Config = {}

-- Command to toggle tag
Config.TagCommand = "tag"

-- Tag settings
Config.TagHeight = 1.0 -- Height above player head
Config.TagDistance = 100.0 -- Maximum distance to see tags
Config.TagScale = 0.8 -- Text scale
Config.TagFont = 4 -- Font type

-- Available tag types
Config.TagTypes = {
    {
        id = 1,
        name = "VIP",
        text = "★ VIP ★",
        color = {255, 215, 0, 255}, -- Gold
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 2,
        name = "Admin",
        text = "⚡ ADMIN ⚡",
        color = {255, 0, 0, 255}, -- Red
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 3,
        name = "Moderator",
        text = "🛡️ MOD 🛡️",
        color = {0, 255, 0, 255}, -- Green
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 4,
        name = "Player",
        text = "👤 PLAYER",
        color = {255, 255, 255, 255}, -- White
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 5,
        name = "Streamer",
        text = "📺 LIVE",
        color = {255, 0, 255, 255}, -- Magenta
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 6,
        name = "Developer",
        text = "💻 DEV",
        color = {0, 255, 255, 255}, -- Cyan
        background = true,
        backgroundColor = {0, 0, 0, 150}
    }
}

-- Default tag for new players
Config.DefaultTag = 4

-- Permission system (set to false to allow everyone)
Config.RequirePermission = false

-- Permissions for specific tags (only used if RequirePermission is true)
Config.TagPermissions = {
    [1] = "tag.vip",
    [2] = "tag.admin", 
    [3] = "tag.moderator",
    [4] = "tag.player",
    [5] = "tag.streamer",
    [6] = "tag.developer"
}

-- Animation settings
Config.EnableAnimation = true
Config.AnimationSpeed = 2.0 -- Speed of floating animation
Config.AnimationHeight = 0.1 -- How much the tag moves up and down
