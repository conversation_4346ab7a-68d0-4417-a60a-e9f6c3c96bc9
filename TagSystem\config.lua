Config = {}

-- Command to toggle tag
Config.TagCommand = "tag"

-- Tag settings
Config.TagHeight = 1.0 -- Height above player head
Config.TagDistance = 100.0 -- Maximum distance to see tags
Config.TagScale = 0.8 -- Text scale
Config.TagFont = 4 -- Font type

-- Available tag types
Config.TagTypes = {
    {
        id = 1,
        name = "VIP",
        type = "image", -- "text" or "image"
        image = "vip_tag.png", -- Image file in images/ folder
        text = "★ VIP ★", -- Fallback text if image fails
        color = {255, 215, 0, 255}, -- Gold
        size = {0.08, 0.08}, -- Width, Height for images
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 2,
        name = "Admin",
        type = "image",
        image = "admin_tag.png",
        text = "⚡ ADMIN ⚡",
        color = {255, 0, 0, 255}, -- Red
        size = {0.08, 0.08},
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 3,
        name = "Moderator",
        type = "image",
        image = "mod_tag.png",
        text = "🛡️ MOD 🛡️",
        color = {0, 255, 0, 255}, -- Green
        size = {0.08, 0.08},
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 4,
        name = "Player",
        type = "text", -- Keep as text for now
        text = "👤 PLAYER",
        color = {255, 255, 255, 255}, -- White
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 5,
        name = "Streamer",
        type = "image",
        image = "streamer_tag.png",
        text = "📺 LIVE",
        color = {255, 0, 255, 255}, -- Magenta
        size = {0.08, 0.08},
        background = true,
        backgroundColor = {0, 0, 0, 150}
    },
    {
        id = 6,
        name = "Developer",
        type = "image",
        image = "dev_tag.png",
        text = "💻 DEV",
        color = {0, 255, 255, 255}, -- Cyan
        size = {0.08, 0.08},
        background = true,
        backgroundColor = {0, 0, 0, 150}
    }
}

-- Default tag for new players
Config.DefaultTag = 4

-- Permission system (set to false to allow everyone)
Config.RequirePermission = false

-- Permissions for specific tags (only used if RequirePermission is true)
Config.TagPermissions = {
    [1] = "tag.vip",
    [2] = "tag.admin", 
    [3] = "tag.moderator",
    [4] = "tag.player",
    [5] = "tag.streamer",
    [6] = "tag.developer"
}

-- Animation settings
Config.EnableAnimation = true
Config.AnimationSpeed = 2.0 -- Speed of floating animation
Config.AnimationHeight = 0.1 -- How much the tag moves up and down
