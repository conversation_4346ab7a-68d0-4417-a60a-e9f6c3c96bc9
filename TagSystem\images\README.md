# Tag System - Custom Images

## 📁 Bild-Ordner für Custom Tags

Legen Sie hier Ihre eigenen Tag-Bilder ab:

### 📋 **Erforderliche Dateien:**
- `vip_tag.png` - VIP Tag Bild
- `admin_tag.png` - Admin Tag Bild  
- `mod_tag.png` - Moderator Tag Bild
- `streamer_tag.png` - Streamer Tag Bild
- `dev_tag.png` - Developer Tag Bild

### 🎨 **Bild-Spezifikationen:**
- **Format:** PNG (empfohlen), JPG oder DDS
- **Größe:** 512x512 Pixel (optimal)
- **Transparenz:** PNG mit Alpha-Kanal unterstützt
- **Dateigröße:** Unter 1MB pro Bild

### 🔧 **Verwendung:**
1. Erstellen Sie Ihre Custom-Bilder
2. Benennen Sie sie entsprechend der config.lua
3. Legen Sie sie in diesen Ordner
4. Starten Sie die Resource neu: `/restart TagSystem`

### 💡 **Tipps:**
- Verwenden Sie kontrastreiche Farben für bessere Sichtbarkeit
- Einfache, klare Designs funktionieren am besten
- Testen Sie verschiedene Größen mit `/tagdebug`

### 🎯 **Beispiel-Konfiguration:**
```lua
{
    id = 1,
    name = "VIP",
    type = "image",
    image = "vip_tag.png",
    size = {0.08, 0.08}, -- Breite, Höhe
}
```

### 🚨 **Fehlerbehebung:**
- Rote Platzhalter = Bild nicht gefunden
- Überprüfen Sie Dateinamen (Groß-/Kleinschreibung)
- Stellen Sie sicher, dass Bilder im richtigen Format sind
